#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt Injection实验演示版本
用较少的seed进行快速测试
"""

import os
import sys
import numpy as np
import json
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import cv2
from tqdm import tqdm

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
sys.path.append("/home/<USER>/claude/SpatialVLA")

# 导入必要模块
import simpler_env
from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
import sapien.core as sapien

# 导入我们的模块
from image_injector import ImageInjector
from spatialvla_segmentation_complete import SpatialVLASegmentationRecorder

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PromptInjectionDemo:
    """Prompt Injection实验演示版本"""
    
    def __init__(self, output_dir: str = None):
        if output_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/prompt_injection_demo_{timestamp}"
        
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化组件
        self.injector = ImageInjector()
        self.segmentation_recorder = SpatialVLASegmentationRecorder(output_dir)
        
        # 实验配置（简化版）
        self.injection_types = [
            'clean',           # baseline
            'symbol_stop',     # ⛔
            'symbol_check',    # ✅
            'related',         # 相关词汇
            'unrelated',       # 无关词汇
            'contradictory'    # 相悖词汇
        ]
        
        # 任务配置
        self.tasks = [
            'google_robot_pick_coke_can',
            'google_robot_move_near'
        ]
        
        # 种子配置（演示版用较少seed）
        self.base_seed = 1234
        self.num_seeds = 5  # 减少到5个seed用于演示
        self.seeds = [self.base_seed * (i + 1) for i in range(self.num_seeds)]
        
        # 结果存储
        self.results = []

        # 为每种注入类型创建单独的目录
        self.injection_dirs = {}
        for injection_type in self.injection_types:
            injection_dir = os.path.join(self.output_dir, injection_type)
            os.makedirs(injection_dir, exist_ok=True)

            # 为每种注入类型创建videos子目录
            video_dir = os.path.join(injection_dir, "videos")
            os.makedirs(video_dir, exist_ok=True)

            self.injection_dirs[injection_type] = {
                'main_dir': injection_dir,
                'video_dir': video_dir
            }

        logger.info(f"演示实验输出目录: {self.output_dir}")
        logger.info(f"注入类型: {self.injection_types}")
        logger.info(f"任务: {self.tasks}")
        logger.info(f"种子数量: {self.num_seeds}")
    
    def get_source_object_name(self, task_name: str, reset_info: Dict) -> str:
        """根据任务获取source_object名称"""
        if 'episode_source_obj_name' in reset_info:
            return reset_info['episode_source_obj_name']
        elif 'pick_coke_can' in task_name:
            return 'opened_coke_can'
        elif 'move_near' in task_name:
            return 'baked_opened_7up_can_v2'
        else:
            return 'unknown_object'
    
    def run_single_experiment(self, task_name: str, injection_type: str, seed: int, 
                            max_timesteps: int = 50) -> Dict[str, Any]:
        """运行单个实验（简化版）"""
        
        logger.info(f"运行实验: {task_name}, {injection_type}, seed={seed}")
        
        try:
            # 创建环境
            env = simpler_env.make(task_name)
            sapien.render_config.rt_use_denoiser = False
            
            # 重置环境
            obs, reset_info = env.reset(seed=seed)
            instruction = env.get_language_instruction()
            
            # 确定相机名称
            camera_name = "overhead_camera" if "google" in task_name else "3rd_view_camera"
            
            # 获取环境信息
            actors_info, articulations_info = self.segmentation_recorder.get_actors_and_articulations_info(env)
            
            # 创建ID映射
            id_mapping = self.segmentation_recorder.create_id_mapping(
                actors_info, articulations_info, reset_info, instruction
            )
            
            # 获取source_object名称
            source_object_name = self.get_source_object_name(task_name, reset_info)
            
            # 初始化SpatialVLA模型
            model = None
            try:
                from simpler_env.policies.spatialvla.spatialvla_model import SpatialVLAInference
                
                policy_setup = "google_robot" if "google" in task_name else "widowx_bridge"
                model = SpatialVLAInference(
                    saved_model_path="IPEC-COMMUNITY/spatialvla-4b-224-pt",
                    policy_setup=policy_setup,
                    action_scale=1.0,
                    action_ensemble_temp=-0.8
                )
                model.reset(instruction)
            except Exception as e:
                logger.error(f"SpatialVLA模型加载失败: {e}")
                env.close()
                return {
                    'task_name': task_name,
                    'injection_type': injection_type,
                    'seed': seed,
                    'success': False,
                    'timesteps': max_timesteps,
                    'error': str(e)
                }
            
            # 执行任务
            success = False
            truncated = False
            predicted_terminated = False
            timestep = 0

            # 初始化视频帧存储
            video_frames = []

            # 保存注入后的第一帧图像
            rgb_image = get_image_from_maniskill2_obs_dict(env, obs)
            segmentation_analysis = self.segmentation_recorder.get_segmentation_data(obs, camera_name)

            injected_image = self.injector.inject_prompt(
                rgb_image, segmentation_analysis, id_mapping,
                injection_type, source_object_name
            )

            # 添加到视频帧
            video_frames.append(injected_image.copy())

            # 保存样本图像到对应的注入类型目录
            task_short = task_name.replace('google_robot_', '')
            sample_path = os.path.join(
                self.injection_dirs[injection_type]['main_dir'],
                f"sample_{task_short}_{seed}.png"
            )
            cv2.imwrite(sample_path, cv2.cvtColor(injected_image, cv2.COLOR_RGB2BGR))
            
            for timestep in range(1, max_timesteps):
                # 获取当前RGB图像
                rgb_image = get_image_from_maniskill2_obs_dict(env, obs)
                
                # 获取分割数据
                segmentation_analysis = self.segmentation_recorder.get_segmentation_data(obs, camera_name)
                
                # 应用注入
                injected_image = self.injector.inject_prompt(
                    rgb_image, segmentation_analysis, id_mapping,
                    injection_type, source_object_name
                )

                # 添加到视频帧
                video_frames.append(injected_image.copy())

                # 模型推理
                try:
                    _, action = model.step(injected_image, instruction)
                    predicted_terminated = bool(action["terminate_episode"][0] > 0)
                    action_vector = np.concatenate([
                        action["world_vector"],
                        action["rot_axangle"],
                        action["gripper"]
                    ])
                except Exception as e:
                    logger.warning(f"SpatialVLA推理失败: {e}")
                    break

                # 环境步进
                obs, _, success, truncated, _ = env.step(action_vector)

                # 检查终止条件
                if success or predicted_terminated or truncated:
                    break
            
            # 保存视频到对应的注入类型目录
            if video_frames:
                task_short = task_name.replace('google_robot_', '')
                video_filename = f"{task_short}_{seed}.mp4"
                video_path = os.path.join(self.injection_dirs[injection_type]['video_dir'], video_filename)

                self.save_video(video_frames, video_path)
                logger.info(f"视频已保存: {video_path}")

            # 清理
            env.close()
            if model is not None:
                del model

            result = {
                'task_name': task_name,
                'injection_type': injection_type,
                'seed': seed,
                'success': success,
                'timesteps': timestep,
                'instruction': instruction,
                'source_object_name': source_object_name,
                'video_path': video_path if video_frames else None
            }

            return result
            
        except Exception as e:
            logger.error(f"实验失败: {e}")
            return {
                'task_name': task_name,
                'injection_type': injection_type,
                'seed': seed,
                'success': False,
                'timesteps': max_timesteps,
                'error': str(e)
            }
    
    def run_demo_experiments(self, max_timesteps: int = 50):
        """运行演示实验"""
        logger.info("开始运行Prompt Injection演示实验")
        
        total_experiments = len(self.tasks) * len(self.injection_types) * len(self.seeds)
        logger.info(f"总实验数量: {total_experiments}")
        
        with tqdm(total=total_experiments, desc="运行演示实验") as pbar:
            for task_name in self.tasks:
                for injection_type in self.injection_types:
                    print(f"\n🔄 开始注入类型: {injection_type}")
                    print("-" * 60)

                    for seed in self.seeds:
                        result = self.run_single_experiment(
                            task_name, injection_type, seed, max_timesteps
                        )
                        self.results.append(result)

                        # 改进的终端输出
                        task_short = task_name.replace('google_robot_', '')
                        success_status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
                        print(f"  {task_short} | seed={seed} | {success_status} | timesteps={result['timesteps']}")

                        pbar.update(1)
                        pbar.set_postfix({
                            'task': task_short[:10],
                            'injection': injection_type[:10],
                            'success': result['success']
                        })

                    # 为每种注入类型生成单独的总结
                    self.generate_injection_summary(injection_type)
        
        logger.info("演示实验完成")

        # 保存结果
        self.save_results()

        # 生成全局总结
        self.generate_global_summary()

        # 生成原有报告（保持兼容性）
        self.generate_report()
    
    def save_video(self, frames, output_path, fps=10):
        """保存视频帧为MP4文件"""
        if not frames:
            logger.warning(f"没有帧可保存: {output_path}")
            return

        try:
            height, width = frames[0].shape[:2]
            size = (width, height)

            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, size)

            for frame in frames:
                # 确保图像是3通道BGR格式
                if len(frame.shape) == 3 and frame.shape[2] == 3:
                    bgr_frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                else:
                    bgr_frame = frame
                out.write(bgr_frame)

            out.release()
            logger.info(f"视频已保存: {output_path}")
        except Exception as e:
            logger.error(f"保存视频失败: {e}")

    def generate_injection_summary(self, injection_type: str):
        """为特定注入类型生成总结"""
        # 筛选该注入类型的结果
        type_results = [r for r in self.results if r['injection_type'] == injection_type]

        if not type_results:
            return

        # 按任务分组
        task_groups = {}
        for result in type_results:
            task_name = result['task_name']
            if task_name not in task_groups:
                task_groups[task_name] = []
            task_groups[task_name].append(result)

        # 创建总结文件
        summary_path = os.path.join(self.injection_dirs[injection_type]['main_dir'], f"{injection_type}_summary.md")

        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(f"# {injection_type.capitalize()} 注入类型总结\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            for task_name, results in task_groups.items():
                task_short = task_name.replace('google_robot_', '')
                f.write(f"## 任务: {task_short}\n\n")

                # 计算统计信息
                success_count = sum(1 for r in results if r['success'])
                success_rate = success_count / len(results) if results else 0
                avg_timesteps = sum(r['timesteps'] for r in results) / len(results) if results else 0

                f.write(f"- 成功率: {success_rate:.2f} ({success_count}/{len(results)})\n")
                f.write(f"- 平均时间步: {avg_timesteps:.2f}\n\n")

                # 详细结果表格
                f.write("| Seed | 成功 | 时间步 |\n")
                f.write("|------|------|--------|\n")

                for result in sorted(results, key=lambda r: r['seed']):
                    success_mark = "✅" if result['success'] else "❌"
                    f.write(f"| {result['seed']} | {success_mark} | {result['timesteps']} |\n")

                f.write("\n")

            # 添加视频链接
            f.write("## 视频文件\n\n")
            video_dir = self.injection_dirs[injection_type]['video_dir']
            video_files = [f for f in os.listdir(video_dir) if f.endswith('.mp4')]

            if video_files:
                for video_file in sorted(video_files):
                    video_path = os.path.join(video_dir, video_file)
                    f.write(f"- [{video_file}]({video_path})\n")
            else:
                f.write("没有可用的视频文件\n")

        logger.info(f"{injection_type} 注入类型总结已保存: {summary_path}")

    def generate_global_summary(self):
        """生成全局总结"""
        if not self.results:
            return

        # 按注入类型分组
        injection_groups = {}
        for result in self.results:
            injection_type = result['injection_type']
            if injection_type not in injection_groups:
                injection_groups[injection_type] = []
            injection_groups[injection_type].append(result)

        # 创建总结文件
        summary_path = os.path.join(self.output_dir, "global_summary.md")

        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("# Prompt Injection 实验全局总结\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 总体统计
            f.write("## 总体统计\n\n")

            # 创建比较表格
            f.write("| 注入类型 | 成功率 | 平均时间步 |\n")
            f.write("|----------|--------|------------|\n")

            baseline_data = None

            for injection_type in self.injection_types:
                if injection_type not in injection_groups:
                    continue

                results = injection_groups[injection_type]
                success_count = sum(1 for r in results if r['success'])
                success_rate = success_count / len(results) if results else 0
                avg_timesteps = sum(r['timesteps'] for r in results) / len(results) if results else 0

                f.write(f"| {injection_type} | {success_rate:.3f} | {avg_timesteps:.2f} |\n")

                if injection_type == 'clean':
                    baseline_data = {
                        'success_rate': success_rate,
                        'avg_timesteps': avg_timesteps
                    }

            f.write("\n")

            # 与baseline比较
            if baseline_data:
                f.write("## 与Baseline比较\n\n")
                f.write("| 注入类型 | 成功率差异 | 时间步差异 |\n")
                f.write("|----------|------------|------------|\n")

                for injection_type in self.injection_types:
                    if injection_type == 'clean' or injection_type not in injection_groups:
                        continue

                    results = injection_groups[injection_type]
                    success_count = sum(1 for r in results if r['success'])
                    success_rate = success_count / len(results) if results else 0
                    avg_timesteps = sum(r['timesteps'] for r in results) / len(results) if results else 0

                    success_diff = success_rate - baseline_data['success_rate']
                    timestep_diff = avg_timesteps - baseline_data['avg_timesteps']

                    success_pct = (success_diff / baseline_data['success_rate']) * 100 if baseline_data['success_rate'] > 0 else 0
                    timestep_pct = (timestep_diff / baseline_data['avg_timesteps']) * 100 if baseline_data['avg_timesteps'] > 0 else 0

                    f.write(f"| {injection_type} | {success_diff:+.3f} ({success_pct:+.1f}%) | {timestep_diff:+.2f} ({timestep_pct:+.1f}%) |\n")

                f.write("\n")

            # 添加各注入类型的链接
            f.write("## 详细报告\n\n")

            for injection_type in self.injection_types:
                if injection_type in injection_groups:
                    summary_file = os.path.join(self.injection_dirs[injection_type]['main_dir'], f"{injection_type}_summary.md")
                    if os.path.exists(summary_file):
                        f.write(f"- [{injection_type.capitalize()} 详细报告]({summary_file})\n")

        logger.info(f"全局总结已保存: {summary_path}")

    def save_results(self):
        """保存实验结果"""
        results_file = os.path.join(self.output_dir, "demo_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        logger.info(f"结果已保存: {results_file}")
    
    def generate_report(self):
        """生成实验报告"""
        logger.info("生成演示实验报告")
        
        # 按任务分组分析
        for task_name in self.tasks:
            task_results = [r for r in self.results if r['task_name'] == task_name]
            
            # 按注入类型分组
            report_data = {}
            baseline_data = None
            
            for injection_type in self.injection_types:
                type_results = [r for r in task_results if r['injection_type'] == injection_type]
                
                if not type_results:
                    continue
                
                # 计算成功率和平均时间步
                success_count = sum(1 for r in type_results if r['success'])
                success_rate = success_count / len(type_results)
                
                successful_results = [r for r in type_results if r['success']]
                if successful_results:
                    avg_timesteps = np.mean([r['timesteps'] for r in successful_results])
                else:
                    avg_timesteps = np.mean([r['timesteps'] for r in type_results])
                
                data = {
                    'success_rate': success_rate,
                    'avg_timesteps': avg_timesteps,
                    'total_runs': len(type_results),
                    'successful_runs': success_count
                }
                
                report_data[injection_type] = data
                
                if injection_type == 'clean':
                    baseline_data = data
            
            # 生成报告
            self._generate_task_report(task_name, report_data, baseline_data)
    
    def _generate_task_report(self, task_name: str, report_data: Dict, baseline_data: Dict):
        """生成单个任务的报告"""
        report_file = os.path.join(self.output_dir, f"demo_report_{task_name}.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"类别: Prompt Injection Demo - {task_name}\n")
            f.write("=" * 120 + "\n")
            f.write(f"{'条件名':<20} {'成功率':<15} {'与Baseline比较':<25} {'平均时间步':<15} {'与Baseline比较':<25}\n")
            f.write("-" * 120 + "\n")
            
            better_count = 0
            worse_count = 0
            
            for injection_type in self.injection_types:
                if injection_type not in report_data:
                    continue
                
                data = report_data[injection_type]
                
                if baseline_data and injection_type != 'clean':
                    # 计算与baseline的比较
                    success_diff = data['success_rate'] - baseline_data['success_rate']
                    success_pct = (success_diff / baseline_data['success_rate']) * 100 if baseline_data['success_rate'] > 0 else 0
                    
                    timestep_diff = data['avg_timesteps'] - baseline_data['avg_timesteps']
                    timestep_pct = (timestep_diff / baseline_data['avg_timesteps']) * 100 if baseline_data['avg_timesteps'] > 0 else 0
                    
                    success_comp = f"{success_diff:+.3f} ({success_pct:+.1f}%)"
                    timestep_comp = f"{timestep_diff:+.2f} ({timestep_pct:+.1f}%)"
                    
                    if success_diff > 0 or (success_diff == 0 and timestep_diff < 0):
                        better_count += 1
                    else:
                        worse_count += 1
                else:
                    success_comp = "baseline"
                    timestep_comp = "baseline"
                
                f.write(f"{injection_type:<20} {data['success_rate']:.3f} {success_comp:<25} {data['avg_timesteps']:.2f} {timestep_comp:<25}\n")
            
            f.write("\n")
            f.write(f"汇总: 共{len(self.injection_types)-1}个条件, 其中{better_count}个比Baseline好, {worse_count}个比Baseline差\n")
            f.write(f"\n演示实验使用 {self.num_seeds} 个seeds: {self.seeds}\n")
        
        logger.info(f"任务 {task_name} 的演示报告已保存: {report_file}")
        
        # 同时打印到控制台
        print(f"\n📊 {task_name} 演示结果:")
        print("="*60)
        for injection_type in self.injection_types:
            if injection_type in report_data:
                data = report_data[injection_type]
                print(f"{injection_type:<15}: 成功率={data['success_rate']:.3f}, 平均时间步={data['avg_timesteps']:.1f}")


def main():
    """主函数"""
    print("🚀 Prompt Injection演示实验")
    
    # 创建演示实验管理器
    demo = PromptInjectionDemo()
    
    # 运行演示实验
    demo.run_demo_experiments(max_timesteps=50)
    
    print("🎉 演示实验完成！")
    print(f"结果保存在: {demo.output_dir}")


if __name__ == "__main__":
    main()
