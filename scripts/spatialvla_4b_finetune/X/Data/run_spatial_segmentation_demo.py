#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SpatialVLA分割视频生成演示脚本
简化版本，用于快速测试和演示功能
"""

import os
import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib
from datetime import datetime
import json
import logging

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 设置matplotlib
matplotlib.rcParams['font.family'] = 'DejaVu Sans'
matplotlib.rcParams['axes.unicode_minus'] = False

# 添加路径
sys.path.append("/home/<USER>/claude/SpatialVLA")

# 导入SimplerEnv
import simpler_env
from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
import sapien.core as sapien

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_simple_segmentation_video(task_name="google_robot_pick_coke_can", 
                                   seed=1234, max_timesteps=30):
    """创建简单的分割视频演示"""
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/segmentation_demo_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    logger.info(f"开始创建分割视频演示")
    logger.info(f"任务: {task_name}")
    logger.info(f"种子: {seed}")
    logger.info(f"输出目录: {output_dir}")
    
    try:
        # 创建环境
        env = simpler_env.make(task_name)
        sapien.render_config.rt_use_denoiser = False
        
        # 重置环境
        obs, reset_info = env.reset(seed=seed)
        instruction = env.get_language_instruction()
        
        logger.info(f"指令: {instruction}")
        logger.info(f"重置信息: {reset_info}")
        
        # 获取actors信息
        actors_info = []
        try:
            actors = env.get_actors()
            for actor in actors:
                actor_info = {
                    'id': getattr(actor, 'id', 'unknown'),
                    'name': getattr(actor, 'name', 'unknown'),
                    'type': type(actor).__name__
                }
                actors_info.append(actor_info)
                logger.info(f"Actor: ID={actor_info['id']}, Name='{actor_info['name']}'")
        except Exception as e:
            logger.warning(f"获取actors信息失败: {e}")
        
        # 收集图像和分割数据
        rgb_images = []
        seg_visualizations = []
        frame_data = []
        
        camera_name = "overhead_camera"
        
        for timestep in range(max_timesteps):
            # 获取RGB图像
            rgb_image = get_image_from_maniskill2_obs_dict(env, obs)
            rgb_images.append(rgb_image)
            
            # 获取分割数据
            segmentation_data = None
            unique_ids = []
            
            try:
                if "image" in obs and camera_name in obs["image"]:
                    cam_data = obs["image"][camera_name]
                    if "Segmentation" in cam_data:
                        segmentation = cam_data["Segmentation"]
                        # 使用第二个通道获取object ID
                        mesh_ids = segmentation[:, :, 1] if segmentation.shape[2] > 1 else segmentation[:, :, 0]
                        unique_ids = np.unique(mesh_ids).tolist()
                        segmentation_data = mesh_ids
                        
                        logger.info(f"帧 {timestep}: 发现 {len(unique_ids)} 个唯一ID: {unique_ids}")
            except Exception as e:
                logger.warning(f"获取分割数据失败: {e}")
            
            # 创建分割可视化
            if segmentation_data is not None:
                # 创建彩色分割图像
                height, width = segmentation_data.shape
                colored_seg = np.zeros((height, width, 3), dtype=np.uint8)
                
                # 为每个ID分配颜色
                colors = plt.cm.tab20(np.linspace(0, 1, len(unique_ids)))
                for i, uid in enumerate(unique_ids):
                    mask = (segmentation_data == uid)
                    color = (colors[i][:3] * 255).astype(np.uint8)
                    colored_seg[mask] = color
                
                seg_visualizations.append(colored_seg)
            else:
                # 如果没有分割数据，创建黑色图像
                seg_visualizations.append(np.zeros_like(rgb_image))
            
            # 记录帧数据
            frame_info = {
                'frame': timestep,
                'unique_ids': unique_ids,
                'instruction': instruction,
                'has_segmentation': segmentation_data is not None
            }
            frame_data.append(frame_info)
            
            # 简单的随机动作（用于演示）
            if timestep < max_timesteps - 1:
                random_action = np.random.uniform(-0.1, 0.1, 7)  # 7维动作空间
                obs, reward, success, truncated, info = env.step(random_action)
                
                if success:
                    logger.info(f"任务在第 {timestep} 步成功完成！")
                    break
        
        # 保存RGB视频
        rgb_video_path = os.path.join(output_dir, f"{task_name}_rgb_demo.mp4")
        save_video_simple(rgb_images, rgb_video_path)
        
        # 保存分割视频
        seg_video_path = os.path.join(output_dir, f"{task_name}_segmentation_demo.mp4")
        save_video_simple(seg_visualizations, seg_video_path)
        
        # 创建并保存组合视频
        combined_images = []
        for i, (rgb_img, seg_img) in enumerate(zip(rgb_images, seg_visualizations)):
            combined_img = create_side_by_side_image(rgb_img, seg_img, f"Frame {i}")
            combined_images.append(combined_img)
        
        combined_video_path = os.path.join(output_dir, f"{task_name}_combined_demo.mp4")
        save_video_simple(combined_images, combined_video_path)
        
        # 保存帧数据
        frame_data_path = os.path.join(output_dir, f"{task_name}_frame_data.json")
        with open(frame_data_path, 'w') as f:
            json.dump({
                'task_name': task_name,
                'instruction': instruction,
                'actors_info': actors_info,
                'frame_data': frame_data
            }, f, indent=2)
        
        # 清理
        env.close()
        
        logger.info("✅ 分割视频演示创建完成！")
        logger.info(f"📁 输出目录: {output_dir}")
        logger.info(f"🎥 RGB视频: {rgb_video_path}")
        logger.info(f"🎨 分割视频: {seg_video_path}")
        logger.info(f"🔗 组合视频: {combined_video_path}")
        logger.info(f"📊 数据文件: {frame_data_path}")
        
        return output_dir
        
    except Exception as e:
        logger.error(f"创建分割视频演示失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def save_video_simple(images, output_path, fps=10):
    """简单的视频保存函数"""
    if not images:
        logger.warning(f"没有图像可保存: {output_path}")
        return
    
    height, width = images[0].shape[:2]
    size = (width, height)
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, size)
    
    for image in images:
        # 确保图像是3通道BGR格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        else:
            bgr_image = image
        out.write(bgr_image)
    
    out.release()
    logger.info(f"视频已保存: {output_path}")

def create_side_by_side_image(rgb_img, seg_img, title=""):
    """创建左右并排的图像"""
    # 确保两个图像大小相同
    if rgb_img.shape != seg_img.shape:
        seg_img = cv2.resize(seg_img, (rgb_img.shape[1], rgb_img.shape[0]))
    
    # 水平拼接
    combined = np.hstack([rgb_img, seg_img])
    
    # 添加标题（可选）
    if title:
        # 在图像顶部添加文字
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(combined, title, (10, 30), font, 1, (255, 255, 255), 2)
    
    return combined

def main():
    """主函数"""
    print("🚀 SpatialVLA分割视频演示")
    
    # 可以测试的任务列表
    test_tasks = [
        "google_robot_pick_coke_can",
        "google_robot_move_near",
        # "google_robot_pick_object",
    ]
    
    for task_name in test_tasks:
        print(f"\n📋 正在处理任务: {task_name}")
        output_dir = create_simple_segmentation_video(
            task_name=task_name,
            seed=1234,
            max_timesteps=20  # 减少时间步用于快速演示
        )
        
        if output_dir:
            print(f"✅ 任务 {task_name} 完成，输出保存在: {output_dir}")
        else:
            print(f"❌ 任务 {task_name} 失败")
    
    print("\n🎉 所有演示任务完成！")

if __name__ == "__main__":
    main()
