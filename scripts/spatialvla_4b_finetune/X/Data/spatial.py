import os

os.environ["CUDA_VISIBLE_DEVICES"] = "0"

import subprocess
import numpy as np
import simpler_env
from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict

import sapien.core as sapien

import cv2
from tqdm import tqdm
import random


def save_video(images, output_path, fps=10):
    height, width, layers = images[0].shape
    size = (width, height)
    out = cv2.VideoWriter(output_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, size)
    for image in images:
        bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        out.write(bgr_image)
    out.release()


task_names = [
    "google_robot_pick_coke_can",
    "google_robot_pick_horizontal_coke_can",
    "google_robot_pick_vertical_coke_can",
    "google_robot_pick_standing_coke_can",
    "google_robot_pick_object",
    "google_robot_move_near_v0",
    "google_robot_move_near_v1",
    "google_robot_move_near",

    # 可以根据需要取消下面任务的注释
    # "google_robot_open_drawer",
    # "google_robot_open_top_drawer",
    # "google_robot_open_middle_drawer",
    # "google_robot_open_bottom_drawer",
    # "google_robot_close_drawer",
    # "google_robot_close_top_drawer",
    # "google_robot_close_middle_drawer",
    # "google_robot_close_bottom_drawer",
    # "google_robot_place_in_closed_drawer",
    # "google_robot_place_in_closed_top_drawer",
    # "google_robot_place_in_closed_middle_drawer",
    # "google_robot_place_in_closed_bottom_drawer",
    # "google_robot_place_apple_in_closed_top_drawer",

    # "widowx_spoon_on_towel",
    # "widowx_carrot_on_plate",
    # "widowx_stack_cube",
    # "widowx_put_eggplant_in_basket",
]

model_name = "spatialvla"  # 将模型名称改为spatialvla
ckpt_path = "IPEC-COMMUNITY/spatialvla-4b-224-pt"  # SpatialVLA模型路径
action_ensemble_temp = -0.8  # 从bash脚本中获取的参数
exp_num = 20
seeds = [i * 1234 for i in range(exp_num)]
max_timestep = 300

os.makedirs('outputs', exist_ok=True)

with open('output_spatialvla_clean.txt', 'w') as f:
    for task_name in task_names:
        # 初始化环境和模型
        print(f"Task: {task_name}    Model: {model_name}", file=f)

        if "google" in task_name:
            policy_setup = "google_robot"
        else:
            policy_setup = "widowx_bridge"

        # 为SpatialVLA加载模型
        from simpler_env.policies.spatialvla.spatialvla_model import SpatialVLAInference

        model = SpatialVLAInference(
            saved_model_path=ckpt_path,
            policy_setup=policy_setup,
            action_scale=1.0,  # 使用默认值
            action_ensemble_temp=action_ensemble_temp  # 添加此参数
        )

        total_frame, total_fail = 0, 0
        for i, seed in zip(range(exp_num), seeds):
            if 'env' in locals():
                print("Closing existing env")
                env.close()
                del env
            env = simpler_env.make(task_name)

            # 关闭降噪以避免内核崩溃
            sapien.render_config.rt_use_denoiser = False

            obs, reset_info = env.reset(seed=seed)
            instruction = env.get_language_instruction()
            model.reset(instruction)
            print("Reset info", reset_info)
            print(f"Instruction: {instruction}")

            image = get_image_from_maniskill2_obs_dict(env, obs)  # np.ndarray of shape (H, W, 3), uint8
            images = [image]
            predicted_terminated, success, truncated = False, False, False
            timestep = 0
            while not (success or predicted_terminated or truncated):  # 添加predicted_terminated作为终止条件
                # 调用模型步进
                raw_action, action = model.step(image, instruction)  # SpatialVLA与OpenVLA相同，都需要instruction参数
                predicted_terminated = bool(action["terminate_episode"][0] > 0)
                obs, reward, success, truncated, info = env.step(
                    np.concatenate([action["world_vector"], action["rot_axangle"], action["gripper"]]))

                # 更新图像观察
                image = get_image_from_maniskill2_obs_dict(env, obs)
                images.append(image)
                timestep += 1
                if timestep >= max_timestep:
                    break

            total_frame += timestep
            if timestep == max_timestep:
                total_fail += 1

            episode_stats = info.get("episode_stats", {})
            print(f"Episode success: {success}  Timestep: {timestep}", file=f)
            save_video(images, f'outputs/{model_name}_{task_name}_{seed}.mp4')
            f.flush()

        print(f"avg timestep: {str(total_frame / exp_num)}    avg fail: {str(total_fail / exp_num)}\n\n", file=f)

        del model

    # 计算评估指标（类似于bash脚本中的最后部分）
    print("🚀 all tasks DONE! Calculating metrics...", file=f)
    # 可选：添加类似于bash脚本中的metrics计算
    # subprocess.run(["python", "tools/calc_metrics_evaluation_videos.py", f"--log-dir-root=outputs"], check=True)