#!/usr/bin/env python3
"""
SpatialVLA with Segmentation ID Mapping
集成分割ID映射功能的SpatialVLA评估脚本
不仅记录RGB视频，还记录每一帧的分割信息并生成分割视频
"""

import os
import sys
import subprocess
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib
from tqdm import tqdm
import random
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 设置matplotlib避免字体问题
matplotlib.rcParams['font.family'] = 'DejaVu Sans'
matplotlib.rcParams['axes.unicode_minus'] = False

# 添加路径
sys.path.append("/home/<USER>/claude/SpatialVLA")

# 导入SimplerEnv
import simpler_env
from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
import sapien.core as sapien

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SegmentationIDMapper:
    """分割ID映射器，用于分析和可视化分割信息"""
    
    def __init__(self):
        # 语义角色颜色映射
        self.semantic_colors = {
            'background': [50, 50, 50],        # 深灰色
            'robot_component': [255, 0, 0],    # 红色
            'source_object': [0, 255, 0],      # 绿色
            'target_object': [0, 0, 255],      # 蓝色
            'distractor_object': [255, 255, 0], # 黄色
            'unknown': [128, 128, 128]         # 灰色
        }
    
    def parse_task_semantics(self, reset_info: Dict = None, instruction: str = None) -> Dict[str, Any]:
        """解析任务语义信息"""
        semantic_info = {
            'source_obj_name': None,
            'target_obj_name': None,
            'instruction_source': None,
            'instruction_target': None,
            'task_type': 'unknown'
        }
        
        # 从reset_info中获取episode信息
        if reset_info:
            semantic_info['source_obj_name'] = reset_info.get('episode_source_obj_name')
            semantic_info['target_obj_name'] = reset_info.get('episode_target_obj_name')
        
        # 从指令中解析
        if instruction:
            instruction_lower = instruction.strip().lower()
            
            if instruction_lower.startswith("pick "):
                semantic_info['task_type'] = 'pick'
                semantic_info['instruction_source'] = instruction_lower.replace("pick ", "", 1).strip()
                
            elif instruction_lower.startswith("move ") and " near " in instruction_lower:
                semantic_info['task_type'] = 'move_near'
                parts = instruction_lower.split(" near ")
                if len(parts) == 2:
                    semantic_info['instruction_source'] = parts[0].replace("move ", "", 1).strip()
                    semantic_info['instruction_target'] = parts[1].strip()
        
        return semantic_info
    
    def determine_semantic_role(self, object_name: str, task_semantic_info: Dict) -> str:
        """根据任务语义信息确定对象的语义角色"""
        name_lower = object_name.lower()
        
        # 检查是否是背景
        if name_lower in ['arena', 'ground', 'background', 'scene', 'stage']:
            return 'background'
        
        # 检查是否是机器人部件
        robot_keywords = ['link_', 'gripper', 'finger', 'arm', 'shoulder', 'elbow', 'wrist', 'base', 'imu', 'camera', 'time_of_flight', 'cliff']
        if any(keyword in name_lower for keyword in robot_keywords):
            return 'robot_component'
        
        # 检查是否是source object
        source_obj_name = task_semantic_info.get('source_obj_name', '').lower()
        instruction_source = task_semantic_info.get('instruction_source', '').lower()
        
        if source_obj_name and source_obj_name in name_lower:
            return 'source_object'
        
        if instruction_source:
            source_keywords = instruction_source.split()
            if any(keyword in name_lower for keyword in source_keywords if len(keyword) > 2):
                return 'source_object'
        
        # 检查是否是target object
        target_obj_name = task_semantic_info.get('target_obj_name', '').lower()
        instruction_target = task_semantic_info.get('instruction_target', '').lower()
        
        if target_obj_name and target_obj_name in name_lower:
            return 'target_object'
        
        if instruction_target:
            target_keywords = instruction_target.split()
            if any(keyword in name_lower for keyword in target_keywords if len(keyword) > 2):
                return 'target_object'
        
        # 其他物体为干扰物
        return 'distractor_object'
    
    def create_id_mapping(self, env, reset_info: Dict, instruction: str) -> Dict[int, Dict[str, Any]]:
        """创建ID到对象的映射"""
        id_mapping = {}
        
        # 解析任务语义信息
        task_semantic_info = self.parse_task_semantics(reset_info, instruction)
        
        try:
            # 获取actors
            actors = env.get_actors()
            for actor in actors:
                actor_id = getattr(actor, 'id', None)
                actor_name = getattr(actor, 'name', 'unknown')
                
                if actor_id is not None:
                    semantic_role = self.determine_semantic_role(actor_name, task_semantic_info)
                    id_mapping[actor_id] = {
                        'name': actor_name,
                        'type': 'Actor',
                        'semantic_role': semantic_role
                    }
        except Exception as e:
            logger.warning(f"Error getting actors: {e}")
        
        try:
            # 获取articulation links
            articulations = env.get_articulations()
            for articulation in articulations:
                if hasattr(articulation, 'get_links'):
                    for link in articulation.get_links():
                        link_id = getattr(link, 'id', None)
                        link_name = getattr(link, 'name', 'unknown')
                        
                        if link_id is not None:
                            id_mapping[link_id] = {
                                'name': link_name,
                                'type': 'ArticulationLink',
                                'semantic_role': 'robot_component'
                            }
        except Exception as e:
            logger.warning(f"Error getting articulations: {e}")
        
        return id_mapping
    
    def get_segmentation_data(self, obs, camera_name: str = "overhead_camera"):
        """获取分割数据"""
        try:
            if "image" in obs and camera_name in obs["image"]:
                cam_data = obs["image"][camera_name]
                if "Segmentation" in cam_data:
                    segmentation = cam_data["Segmentation"]
                    # 使用第二个通道获取object ID
                    mesh_ids = segmentation[:, :, 1] if segmentation.shape[2] > 1 else segmentation[:, :, 0]
                    return mesh_ids
        except Exception as e:
            logger.warning(f"Error getting segmentation data: {e}")
        
        return None
    
    def create_segmentation_visualization(self, mesh_ids: np.ndarray, id_mapping: Dict,
                                        frame_idx: int = 0) -> np.ndarray:
        """创建分割可视化图像"""
        if mesh_ids is None:
            return np.zeros((480, 640, 3), dtype=np.uint8)

        # 创建彩色分割图像
        height, width = mesh_ids.shape
        colored_seg = np.zeros((height, width, 3), dtype=np.uint8)

        unique_ids = np.unique(mesh_ids)

        for uid in unique_ids:
            mask = (mesh_ids == uid)

            if uid in id_mapping:
                semantic_role = id_mapping[uid]['semantic_role']
                color = self.semantic_colors.get(semantic_role, self.semantic_colors['unknown'])
            else:
                color = self.semantic_colors['unknown']

            colored_seg[mask] = color

        return colored_seg

    def create_combined_visualization(self, rgb_image: np.ndarray, mesh_ids: np.ndarray,
                                    id_mapping: Dict, frame_idx: int = 0) -> np.ndarray:
        """创建RGB和分割的组合可视化图像"""
        if mesh_ids is None:
            # 如果没有分割数据，返回原始RGB图像
            return rgb_image

        # 创建分割可视化
        seg_vis = self.create_segmentation_visualization(mesh_ids, id_mapping, frame_idx)

        # 创建2x2布局的组合图像
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))

            # 原始RGB图像
            axes[0, 0].imshow(rgb_image)
            axes[0, 0].set_title(f'RGB Frame {frame_idx}')
            axes[0, 0].axis('off')

            # 分割可视化
            axes[0, 1].imshow(seg_vis)
            axes[0, 1].set_title(f'Segmentation Frame {frame_idx}')
            axes[0, 1].axis('off')

            # 分割ID统计
            unique_ids, counts = np.unique(mesh_ids, return_counts=True)
            axes[1, 0].bar(range(len(unique_ids)), counts)
            axes[1, 0].set_xlabel('Object ID')
            axes[1, 0].set_ylabel('Pixel Count')
            axes[1, 0].set_title('Pixel Count by ID')
            axes[1, 0].set_xticks(range(len(unique_ids)))
            axes[1, 0].set_xticklabels([str(uid) for uid in unique_ids], rotation=45)

            # ID映射信息
            axes[1, 1].axis('off')
            mapping_text = f"Frame {frame_idx} ID Mapping:\n" + "="*25 + "\n"

            # 按像素数量排序显示前10个
            sorted_data = sorted(zip(unique_ids, counts), key=lambda x: x[1], reverse=True)[:10]

            for uid, pixel_count in sorted_data:
                if uid in id_mapping:
                    name = id_mapping[uid]['name']
                    role = id_mapping[uid]['semantic_role']
                    mapping_text += f"ID {uid}: {name}\n"
                    mapping_text += f"  Role: {role}\n"
                    mapping_text += f"  Pixels: {pixel_count}\n\n"
                else:
                    mapping_text += f"ID {uid}: Unknown\n"
                    mapping_text += f"  Pixels: {pixel_count}\n\n"

            axes[1, 1].text(0.05, 0.95, mapping_text, transform=axes[1, 1].transAxes,
                           fontsize=8, verticalalignment='top', fontfamily='monospace')
            axes[1, 1].set_title('ID Mapping Details')

            plt.tight_layout()

            # 将matplotlib图像转换为numpy数组
            fig.canvas.draw()
            buf = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
            buf = buf.reshape(fig.canvas.get_width_height()[::-1] + (3,))
            plt.close(fig)

            return buf

        except Exception as e:
            logger.error(f"Error creating combined visualization: {e}")
            # 如果可视化失败，返回原始RGB图像
            return rgb_image

def save_video(images, output_path, fps=10):
    """保存图像序列为视频"""
    if not images:
        logger.warning(f"No images to save for {output_path}")
        return

    height, width = images[0].shape[:2]
    size = (width, height)
    out = cv2.VideoWriter(output_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, size)

    for image in images:
        # 确保图像是3通道的
        if len(image.shape) == 3 and image.shape[2] == 3:
            bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        else:
            bgr_image = image
        out.write(bgr_image)

    out.release()
    logger.info(f"Video saved: {output_path}")

def save_segmentation_info(segmentation_data: List[Dict], output_path: str):
    """保存分割信息到JSON文件"""
    try:
        with open(output_path, 'w') as f:
            json.dump(segmentation_data, f, indent=2, default=str)
        logger.info(f"Segmentation info saved: {output_path}")
    except Exception as e:
        logger.error(f"Error saving segmentation info: {e}")

def main():
    """主函数"""
    # 配置参数
    task_names = [
        "google_robot_pick_coke_can",
        "google_robot_move_near",
        # 可以添加更多任务
    ]

    model_name = "spatialvla"
    ckpt_path = "IPEC-COMMUNITY/spatialvla-4b-224-pt"
    action_ensemble_temp = -0.8
    exp_num = 2  # 减少实验数量用于测试
    seeds = [1234, 2468]  # 使用固定种子
    max_timestep = 50  # 减少时间步用于测试
    
    # 创建输出目录
    output_root = "/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/X/Data"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(output_root, f"spatialvla_segmentation_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化分割映射器
    seg_mapper = SegmentationIDMapper()
    
    # 创建结果文件
    results_file = os.path.join(output_dir, 'results.txt')
    
    with open(results_file, 'w') as f:
        logger.info(f"Starting SpatialVLA evaluation with segmentation mapping")
        logger.info(f"Output directory: {output_dir}")
        
        for task_name in task_names:
            logger.info(f"Processing task: {task_name}")
            print(f"Task: {task_name}    Model: {model_name}", file=f)
            
            # 确定policy setup
            if "google" in task_name:
                policy_setup = "google_robot"
                camera_name = "overhead_camera"
            else:
                policy_setup = "widowx_bridge"
                camera_name = "3rd_view_camera"
            
            # 加载SpatialVLA模型
            from simpler_env.policies.spatialvla.spatialvla_model import SpatialVLAInference
            
            model = SpatialVLAInference(
                saved_model_path=ckpt_path,
                policy_setup=policy_setup,
                action_scale=1.0,
                action_ensemble_temp=action_ensemble_temp
            )
            
            total_frame, total_fail = 0, 0
            
            for i, seed in zip(range(exp_num), seeds):
                logger.info(f"Running episode {i+1}/{exp_num} with seed {seed}")
                
                # 清理之前的环境
                if 'env' in locals():
                    env.close()
                    del env
                
                # 创建环境
                env = simpler_env.make(task_name)
                sapien.render_config.rt_use_denoiser = False
                
                # 重置环境
                obs, reset_info = env.reset(seed=seed)
                instruction = env.get_language_instruction()
                model.reset(instruction)
                
                logger.info(f"Instruction: {instruction}")
                
                # 创建ID映射
                id_mapping = seg_mapper.create_id_mapping(env, reset_info, instruction)
                
                # 初始化图像和分割数据列表
                rgb_images = []
                seg_images = []
                combined_images = []
                segmentation_data = []

                # 获取初始观察
                rgb_image = get_image_from_maniskill2_obs_dict(env, obs)
                mesh_ids = seg_mapper.get_segmentation_data(obs, camera_name)
                seg_image = seg_mapper.create_segmentation_visualization(mesh_ids, id_mapping, 0)
                combined_image = seg_mapper.create_combined_visualization(rgb_image, mesh_ids, id_mapping, 0)

                rgb_images.append(rgb_image)
                seg_images.append(seg_image)
                combined_images.append(combined_image)

                # 记录分割信息
                frame_info = {
                    'frame': 0,
                    'unique_ids': np.unique(mesh_ids).tolist() if mesh_ids is not None else [],
                    'id_mapping': {str(k): v for k, v in id_mapping.items()},
                    'instruction': instruction
                }
                segmentation_data.append(frame_info)
                
                # 执行任务
                predicted_terminated, success, truncated = False, False, False
                timestep = 0
                
                while not (success or predicted_terminated or truncated):
                    # 模型推理
                    _, action = model.step(rgb_image, instruction)
                    predicted_terminated = bool(action["terminate_episode"][0] > 0)

                    # 环境步进
                    obs, _, success, truncated, info = env.step(
                        np.concatenate([action["world_vector"], action["rot_axangle"], action["gripper"]])
                    )

                    # 更新观察
                    rgb_image = get_image_from_maniskill2_obs_dict(env, obs)
                    mesh_ids = seg_mapper.get_segmentation_data(obs, camera_name)
                    seg_image = seg_mapper.create_segmentation_visualization(mesh_ids, id_mapping, timestep + 1)
                    combined_image = seg_mapper.create_combined_visualization(rgb_image, mesh_ids, id_mapping, timestep + 1)

                    rgb_images.append(rgb_image)
                    seg_images.append(seg_image)
                    combined_images.append(combined_image)

                    # 记录分割信息
                    frame_info = {
                        'frame': timestep + 1,
                        'unique_ids': np.unique(mesh_ids).tolist() if mesh_ids is not None else [],
                        'action': {k: v.tolist() if hasattr(v, 'tolist') else v for k, v in action.items()},
                        'predicted_terminated': predicted_terminated,
                        'success': success
                    }
                    segmentation_data.append(frame_info)

                    timestep += 1
                    if timestep >= max_timestep:
                        break
                
                total_frame += timestep
                if timestep == max_timestep:
                    total_fail += 1
                
                # 保存视频和数据
                episode_prefix = f"{model_name}_{task_name}_{seed}"

                # 保存RGB视频
                rgb_video_path = os.path.join(output_dir, f"{episode_prefix}_rgb.mp4")
                save_video(rgb_images, rgb_video_path)

                # 保存分割视频
                seg_video_path = os.path.join(output_dir, f"{episode_prefix}_segmentation.mp4")
                save_video(seg_images, seg_video_path)

                # 保存组合视频（RGB + 分割 + 统计信息）
                combined_video_path = os.path.join(output_dir, f"{episode_prefix}_combined.mp4")
                save_video(combined_images, combined_video_path)

                # 保存分割信息
                seg_info_path = os.path.join(output_dir, f"{episode_prefix}_segmentation_info.json")
                save_segmentation_info(segmentation_data, seg_info_path)

                result_line = f"Episode {i+1}: Success={success}, Timestep={timestep}, Seed={seed}"
                logger.info(result_line)
                print(result_line, file=f)
                f.flush()
            
            avg_timestep = total_frame / exp_num
            avg_fail = total_fail / exp_num
            summary_line = f"Task {task_name}: avg timestep={avg_timestep:.2f}, avg fail={avg_fail:.2f}"
            logger.info(summary_line)
            print(summary_line + "\n", file=f)
            
            del model
    
    logger.info("🚀 All tasks completed!")
    logger.info(f"Results saved in: {output_dir}")

if __name__ == "__main__":
    main()
